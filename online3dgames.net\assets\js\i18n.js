/**
 * 国际化语言管理系统
 * 支持动态语言切换、自动检测、本地存储
 */
class I18nManager {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.supportedLanguages = ['en', 'de'];
        this.defaultLanguage = 'en';
        
        // 初始化
        this.init();
    }

    async init() {
        // 检测用户语言偏好
        this.detectLanguage();
        
        // 加载翻译文件
        await this.loadTranslations();
        
        // 应用翻译
        this.applyTranslations();
        
        // 设置语言切换按钮
        this.setupLanguageSwitch();
        
        // 更新SEO元素
        this.updateSEOElements();
    }

    detectLanguage() {
        // 1. 检查本地存储的用户偏好
        const savedLanguage = localStorage.getItem('preferred-language');
        if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
            this.currentLanguage = savedLanguage;
            return;
        }

        // 2. 检查浏览器语言
        const browserLanguage = navigator.language || navigator.userLanguage;
        if (browserLanguage.startsWith('de')) {
            this.currentLanguage = 'de';
        } else {
            this.currentLanguage = this.defaultLanguage;
        }

        // 保存检测到的语言
        localStorage.setItem('preferred-language', this.currentLanguage);
    }

    async loadTranslations() {
        try {
            // 获取当前游戏路径
            const gamePath = this.getGamePath();
            const response = await fetch(`${gamePath}/i18n/${this.currentLanguage}.json`);
            if (!response.ok) {
                throw new Error(`Failed to load ${this.currentLanguage} translations`);
            }
            this.translations = await response.json();
        } catch (error) {
            console.warn(`Failed to load translations for ${this.currentLanguage}, falling back to English`);
            if (this.currentLanguage !== this.defaultLanguage) {
                this.currentLanguage = this.defaultLanguage;
                const gamePath = this.getGamePath();
                const response = await fetch(`${gamePath}/i18n/${this.currentLanguage}.json`);
                this.translations = await response.json();
            }
        }
    }

    getGamePath() {
        // 根据当前URL确定游戏路径
        const path = window.location.pathname;
        if (path.includes('/blackjack-practice')) {
            return '/blackjack-practice';
        } else if (path.includes('/blackjack')) {
            return '/blackjack';
        } else if (path.includes('/freeBetBlackjack')) {
            return '/freeBetBlackjack';
        } else if (path.includes('/pontoon-game')) {
            return '/pontoon-game';
        }
        // 默认返回当前目录
        return window.location.pathname.replace(/\/[^\/]*$/, '') || '/blackjack-practice';
    }

    applyTranslations() {
        // 翻译所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.getTranslation(key);
            
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'button') {
                    element.value = translation;
                } else if (element.hasAttribute('title')) {
                    element.title = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });

        // 翻译所有带有 data-i18n-placeholder 属性的输入框
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            const translation = this.getTranslation(key);
            if (translation) {
                element.placeholder = translation;
            }
        });
    }

    getTranslation(key) {
        const keys = key.split('.');
        let value = this.translations;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return null;
            }
        }
        
        return value;
    }

    // 格式化带参数的翻译
    formatTranslation(key, params = {}) {
        let translation = this.getTranslation(key);
        if (!translation) return key;

        // 替换参数占位符
        Object.keys(params).forEach(param => {
            const placeholder = `{${param}}`;
            translation = translation.replace(new RegExp(placeholder, 'g'), params[param]);
        });

        return translation;
    }

    setupLanguageSwitch() {
        // 创建语言切换按钮
        const languageButton = document.createElement('button');
        languageButton.className = 'language-switch-btn';
        languageButton.innerHTML = this.getTranslation('ui.languageSwitch') || '🇩🇪 Deutsch';
        languageButton.title = 'Switch Language / Sprache wechseln';

        // 添加到控制区域
        const rightControls = document.querySelector('.right-controls');
        if (rightControls) {
            rightControls.insertBefore(languageButton, rightControls.firstChild);
        }

        // 绑定点击事件
        languageButton.addEventListener('click', () => {
            this.switchLanguage();
        });
    }

    // 供外部调用的语言切换方法
    async changeLanguage(lang) {
        if (this.supportedLanguages.includes(lang) && lang !== this.currentLanguage) {
            this.currentLanguage = lang;
            localStorage.setItem('preferred-language', this.currentLanguage);
            await this.loadTranslations();
            this.applyTranslations();
            this.updateLanguageButton();
            this.updateSEOElements();

            // 触发自定义事件
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: this.currentLanguage }
            }));
        }
    }

    updateLanguageButton() {
        const languageButton = document.querySelector('.language-switch-btn');
        if (languageButton) {
            languageButton.innerHTML = this.getTranslation('ui.languageSwitch');
        }
    }

    async switchLanguage() {
        // 切换语言
        const newLanguage = this.currentLanguage === 'en' ? 'de' : 'en';
        await this.changeLanguage(newLanguage);
    }

    updateSEOElements() {
        // 更新页面标题
        const title = this.getTranslation('meta.title');
        if (title) {
            document.title = title;
        }

        // 更新meta描述
        const description = this.getTranslation('meta.description');
        if (description) {
            const metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc) metaDesc.content = description;
        }

        // 更新keywords
        const keywords = this.getTranslation('meta.keywords');
        if (keywords) {
            const metaKeywords = document.querySelector('meta[name="keywords"]');
            if (metaKeywords) metaKeywords.content = keywords;
        }

        // 更新Open Graph标签
        const ogTitle = this.getTranslation('meta.ogTitle');
        if (ogTitle) {
            const ogTitleMeta = document.querySelector('meta[property="og:title"]');
            if (ogTitleMeta) ogTitleMeta.content = ogTitle;
        }

        const ogDescription = this.getTranslation('meta.ogDescription');
        if (ogDescription) {
            const ogDescMeta = document.querySelector('meta[property="og:description"]');
            if (ogDescMeta) ogDescMeta.content = ogDescription;
        }

        // 更新Twitter标签
        const twitterTitle = this.getTranslation('meta.twitterTitle');
        if (twitterTitle) {
            const twitterTitleMeta = document.querySelector('meta[name="twitter:title"]');
            if (twitterTitleMeta) twitterTitleMeta.content = twitterTitle;
        }

        const twitterDescription = this.getTranslation('meta.twitterDescription');
        if (twitterDescription) {
            const twitterDescMeta = document.querySelector('meta[name="twitter:description"]');
            if (twitterDescMeta) twitterDescMeta.content = twitterDescription;
        }

        // 更新HTML lang属性
        document.documentElement.lang = this.currentLanguage;
    }

    // 供游戏代码调用的翻译方法
    t(key, params = {}) {
        return this.formatTranslation(key, params);
    }

    // 获取当前语言
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // 检查是否为德语
    isGerman() {
        return this.currentLanguage === 'de';
    }
}

// 创建全局实例
window.i18n = new I18nManager();
